apiVersion: apps/v1
kind: Deployment
metadata:
  name: jaijaipay-snapshot-app
  labels:
    app.name: jaija<PERSON>ay-snapshot-app
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.name: jaijaipay-snapshot-app
  template:
    metadata:
      labels:
        app.name: jaijaipay-snapshot-app
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kops.k8s.io/instancegroup
                    operator: In
                    values:
                      - nodes-jaijaipay-hc-amd64-apse-1a
      containers:
        - name: jaijaipay-snapshot-app
          image: 338460195757.dkr.ecr.ap-southeast-1.amazonaws.com/jaijaipay-snapshot-app:latest
          envFrom:
          - secretRef:
              name: jaijaipay-snapshot-app-secret
          imagePullPolicy: Always
          resources: 
            requests:
              cpu: 1000m
              memory: 128Mi
            limits:
              cpu: 1000m
              memory: 512Mi
          livenessProbe:
            failureThreshold: 10
            initialDelaySeconds: 60
            periodSeconds: 90
            timeoutSeconds: 120
          lifecycle:
            preStop:
              exec:
                command: ["sleep", "10"]
      imagePullSecrets:
        - name: ecr-registry
      restartPolicy: Always


