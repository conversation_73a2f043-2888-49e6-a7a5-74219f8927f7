FROM python:3.13.2-slim AS builder
RUN apt-get update -y
RUN apt-get install -y --no-install-recommends ca-certificates curl g++ gcc git
RUN curl --proto '=https' --tlsv1.2 -LsSf https://github.com/astral-sh/uv/releases/download/0.4.30/uv-installer.sh | sh
ENV PATH="/root/.cargo/bin/:$PATH"
WORKDIR /app
COPY . .
RUN echo 3.13.2 > .python-version && uv venv --python-preference system
RUN uv sync --frozen --no-dev

FROM python:3.13.2-slim AS runner
COPY --from=builder /app/src /app/src
COPY --from=builder /app/.venv/lib/python3.13/site-packages /usr/local/lib/python3.13/site-packages
ENV PYTHONUNBUFFERED=1
ENV DOCKER_ENV=1
WORKDIR /app/src
CMD [ "python", "jai<PERSON><PERSON><PERSON>_backtesting/scheduler.py" ]
