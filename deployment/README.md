# jaijaipay Backtesting API

## Description
jaijaipay Backtesting API is a powerful tool for Backtesting Beta.

### Installation
1. Clone the repository:
    ```bash
    git clone https://github.com/JaiJaiPay/jaijaipay-snapshot-report
    ```

2. (For first time deployment) Set up Kubernetes secret:
    ```bash
        kubectl create secret generic jaijaipay-snapshot-app-secret \
    --namespace jaijaipay-snapshot-prod \
    --from-literal=DATABASE_URL=<DATABASE_URL> \
    ```