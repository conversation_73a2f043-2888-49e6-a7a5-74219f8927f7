### Standard packages ###
from functools import lru_cache

### Third-party packages ###
from pydantic_settings import BaseSettings, SettingsConfigDict


class EnvSettings(BaseSettings):
    # DATABASE
    DATABASE_URL: str
    # AWS
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


env_settings = EnvSettings()


@lru_cache
def get_settings() -> EnvSettings:
    return env_settings


# load env
ENV_VARS: EnvSettings = get_settings()

__all__: tuple[str, ...] = "ENV_VARS"
