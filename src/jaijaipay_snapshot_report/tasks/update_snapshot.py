from typing import Sequence, Tuple, Optional, List, Dict, Any
from sqlalchemy import Insert, Result, Select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, insert
from datetime import datetime
from decimal import Decimal

#
import uuid
import polars as pl

#
from src.jaijaipay_snapshot_report.database import BankAccounts, SnapshotTransactions, Transactions, async_session


async def create_snapshot_transaction() -> None:
    """
    Creates or updates snapshot transactions for active bank accounts.
    Groups transactions by hour and maintains cumulative totals.
    """
    async with async_session(expire_on_commit=False) as session:
        try:
            # Get active bank accounts
            active_bank_accounts: List[BankAccounts] = await _get_active_bank_accounts(session=session)

            if not active_bank_accounts:
                print("No active bank accounts found")
                return

            # # Process each bank account
            for bank_account_id in active_bank_accounts:
                await _process_bank_account_snapshots(session=session, bank_account_id=bank_account_id.id)

        except Exception as e:
            print(f"Error in create_snapshot_transaction: {e}")
            await session.rollback()


async def _get_active_bank_accounts(session: AsyncSession) -> List[str]:
    """Get list of active bank account IDs."""
    query: Select[Tuple[uuid.UUID]] = select(BankAccounts).where(BankAccounts.is_active)
    result: Result[Tuple[uuid.UUID]] = await session.execute(statement=query)
    bank_accounts: Sequence[BankAccounts] = result.scalars().all()
    return bank_accounts


async def _get_last_snapshot_time(session: AsyncSession, bank_account_id: uuid.UUID) -> Optional[SnapshotTransactions]:
    """Get the last completed timestamp for a bank account's snapshots."""
    query: Select[Tuple[datetime]] = (
        select(SnapshotTransactions)
        .where(SnapshotTransactions.target_platform_bank_account_id == bank_account_id)
        .order_by(SnapshotTransactions.last_completed_at.desc())
        .limit(limit=1)
    )
    result: Result[Tuple[datetime]] = await session.execute(statement=query)
    snapshot: SnapshotTransactions | None = result.scalar_one_or_none()
    return snapshot


async def _get_transactions_since(
    session: AsyncSession,
    bank_account_id: uuid.UUID,
    last_snapshot_time: Optional[SnapshotTransactions] = None,
) -> List[Dict[str, Any]]:
    """Get completed transactions since a given time."""
    query: Select[Tuple[Transactions]] = (
        select(Transactions)
        .where(Transactions.status == "COMPLETED")
        .where(Transactions.target_platform_bank_account_id == bank_account_id)
    )

    if last_snapshot_time:
        query = query.where(Transactions.completed_at > last_snapshot_time.last_completed_at)

    result: Result[Tuple[Transactions]] = await session.execute(statement=query)
    transactions: Sequence[Transactions] = result.scalars().all()

    if not transactions:
        return []

    return [
        {
            "id": str(t.id),
            "merchant_id": str(t.merchant_id),
            "company_id": str(t.company_id),
            "transaction_type": t.transaction_type,
            "currency": t.currency,
            "target_platform_bank_account_id": t.target_platform_bank_account_id,
            "amount_requested": (t.amount_requested),
            "amount_client_paid": t.amount_client_paid,
            "amount_client_received": t.amount_client_received,
            "amount_company_received": t.amount_company_received,
            "total_fee_amount": t.total_fee_amount,
            "total_commission_amount": t.total_commission_amount,
            "available_balance": t.available_balance,
            "completed_at": t.completed_at,
        }
        for t in transactions
    ]


def _group_transactions_by_hour(transactions: List[Dict[str, Any]]) -> pl.DataFrame:
    """Group transactions by hour using Polars."""
    if not transactions:
        return pl.DataFrame()

    df: pl.DataFrame = pl.DataFrame(data=transactions).sort(by="completed_at")
    df = df.with_columns(
        pl.col(name="amount_requested").fill_null(value=Decimal(value=0.00)),
        pl.col(name="amount_client_paid").fill_null(value=Decimal(value=0.00)),
        pl.col(name="amount_client_received").fill_null(value=Decimal(value=0.00)),
        pl.col(name="amount_company_received").fill_null(value=Decimal(value=0.00)),
        pl.col(name="total_fee_amount").fill_null(value=Decimal(value=0.00)),
        pl.col(name="total_commission_amount").fill_null(value=Decimal(value=0.00)),
        pl.col(name="available_balance").fill_null(value=Decimal(value=0.00)),
    )

    # Add hourly grouping column
    df = df.with_columns(pl.col(name="completed_at").dt.strftime(format="%Y-%m-%d %H").alias(name="completion_hour"))

    # Group by key dimensions and hour
    grouped: pl.DataFrame = df.group_by(
        [
            "merchant_id",
            "company_id",
            "transaction_type",
            "currency",
            "completion_hour",
        ]
    ).agg(
        [
            pl.col(name="id").count().alias(name="counts_transaction"),
            pl.col(name="amount_requested").sum().alias(name="sum_amount_requested"),
            pl.col(name="amount_client_paid").sum().alias(name="sum_amount_client_paid"),
            pl.col(name="amount_client_received").sum().alias(name="sum_amount_client_received"),
            pl.col(name="amount_company_received").sum().alias(name="sum_amount_company_received"),
            pl.col(name="total_fee_amount").sum().alias(name="sum_total_fee_amount"),
            pl.col(name="total_commission_amount").sum().alias(name="sum_total_commission_amount"),
            pl.col(name="available_balance").last().alias(name="last_available_balance"),
            pl.col(name="target_platform_bank_account_id").last().alias(name="target_platform_bank_account_id"),
            pl.col(name="completed_at").min().alias(name="start_completed_at"),
            pl.col(name="completed_at").max().alias(name="last_completed_at"),
        ]
    )

    return grouped.sort(by="last_completed_at").drop("completion_hour")


async def _get_latest_snapshot(
    session: AsyncSession,
    merchant_id: str,
    company_id: str,
    transaction_type: str,
    currency: str,
    target_platform_bank_account_id: str,
) -> Optional[SnapshotTransactions]:
    """Get the latest snapshot for given identifiers."""
    query: Select[Tuple[SnapshotTransactions]] = select(SnapshotTransactions).where(
        SnapshotTransactions.company_id == company_id,
        SnapshotTransactions.transaction_type == transaction_type,
        SnapshotTransactions.currency == currency,
        SnapshotTransactions.target_platform_bank_account_id == target_platform_bank_account_id,
    )
    # Add merchant_id condition only if it's not None
    if merchant_id is not None and merchant_id != "None":
        query = query.where(SnapshotTransactions.merchant_id == merchant_id)
    elif merchant_id is None:
        query = query.where(SnapshotTransactions.merchant_id.is_(other=None))
    else:
        print("merchant_id is None")

    query = query.order_by(SnapshotTransactions.last_completed_at.desc()).limit(limit=1)
    result: Result[Tuple[SnapshotTransactions]] = await session.execute(statement=query)
    return result.scalar_one_or_none()


def _should_create_new_snapshot(last_snapshot: Optional[SnapshotTransactions], current_completed_at: datetime) -> bool:
    """Determine if a new snapshot should be created or existing one updated."""
    if last_snapshot is None:
        return True

    # Compare hours (truncated to hour precision)
    current_hour: datetime = current_completed_at.replace(minute=0, second=0, microsecond=0)
    last_hour: datetime = last_snapshot.last_completed_at.replace(minute=0, second=0, microsecond=0)

    return current_hour != last_hour


def _calculate_cumulative_values(row: Dict[str, Any], last_snapshot: Optional[SnapshotTransactions]) -> Dict[str, Any]:
    """Calculate cumulative values for snapshot."""
    if row.get("merchant_id") == "None":
        row["merchant_id"] = None

    if last_snapshot is None:
        return row.copy()

    # Add current values to previous cumulative totals
    cumulative_row: Dict[str, Any] = row.copy()
    cumulative_row.update(
        {
            "sum_amount_requested": last_snapshot.sum_amount_requested + row["sum_amount_requested"],
            "sum_amount_client_paid": last_snapshot.sum_amount_client_paid + row["sum_amount_client_paid"],
            "sum_amount_client_received": last_snapshot.sum_amount_client_received + row["sum_amount_client_received"],
            "sum_amount_company_received": last_snapshot.sum_amount_company_received
            + row["sum_amount_company_received"],
            "sum_total_fee_amount": last_snapshot.sum_total_fee_amount + row["sum_total_fee_amount"],
            "sum_total_commission_amount": last_snapshot.sum_total_commission_amount
            + row["sum_total_commission_amount"],
            "counts_transaction": last_snapshot.counts_transaction + row["counts_transaction"],
        }
    )

    return cumulative_row


async def _process_bank_account_snapshots(session: AsyncSession, bank_account_id: uuid.UUID) -> None:
    """Process snapshots for a single bank account."""
    # Get last snapshot time for this bank account
    last_snapshot_time: SnapshotTransactions | None = await _get_last_snapshot_time(
        session=session,
        bank_account_id=bank_account_id,
    )

    # Get transactions since last snapshot
    transactions: List[Dict[str, Any]] = await _get_transactions_since(
        session=session,
        bank_account_id=bank_account_id,
        last_snapshot_time=last_snapshot_time,
    )

    if not transactions:
        print(f"No Transactions For Bank: {bank_account_id}")
        return

    # Group transactions by hour
    grouped_transactions: pl.DataFrame = _group_transactions_by_hour(transactions=transactions)

    # Check if there are any grouped transactions
    if grouped_transactions.height == 0:
        print(f"No Grouped Transactions For Bank: {bank_account_id}")
        return
    # Process each group
    for row in grouped_transactions.iter_rows(named=True):
        last_snapshot: SnapshotTransactions | None = await _get_latest_snapshot(
            session=session,
            merchant_id=row["merchant_id"],
            company_id=row["company_id"],
            transaction_type=row["transaction_type"],
            currency=row["currency"],
            target_platform_bank_account_id=row["target_platform_bank_account_id"],
        )
        # Determine if we should create new snapshot or update existing
        should_create: bool = _should_create_new_snapshot(
            last_snapshot=last_snapshot,
            current_completed_at=row["last_completed_at"],
        )
        # Calculate cumulative values
        cumulative_values: Dict[str, Any] = _calculate_cumulative_values(row=row, last_snapshot=last_snapshot)
        if should_create:
            print(
                f"Creating at {row['last_completed_at']}-"
                f"[{row['merchant_id']}|{row['company_id']}|{row['transaction_type']}|"
                f"{row['currency']}|{row['target_platform_bank_account_id']}]"
            )
            stmt: Insert = insert(table=SnapshotTransactions).values(**cumulative_values)
        else:
            print(
                f"Updating at {row['last_completed_at']}-"
                f"[{row['merchant_id']}|{row['company_id']}|{row['transaction_type']}|"
                f"{row['currency']}|{row['target_platform_bank_account_id']}]"
            )
            # Remove start_completed_at for updates
            del cumulative_values["start_completed_at"]
            stmt = (
                update(table=SnapshotTransactions)
                .where(SnapshotTransactions.id == last_snapshot.id)
                .values(**cumulative_values)
            )
        await session.execute(statement=stmt)
        await session.commit()


if __name__ == "__main__":
    import asyncio

    async def main():
        await create_snapshot_transaction()

    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Error: {e}")
