from sqlalchemy.ext.asyncio.engine import AsyncEngine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

# local
from src.jaijaipay_snapshot_report.configs import ENV_VARS

engine: AsyncEngine = create_async_engine(
    url=ENV_VARS.DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_timeout=30,
    pool_recycle=1800,
    pool_pre_ping=True,
)
async_session: async_sessionmaker[AsyncSession] = async_sessionmaker(
    bind=engine,
    class_=AsyncSession,
)
