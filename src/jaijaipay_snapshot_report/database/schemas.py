from sqlalchemy import Column, String, DECIMAL, DateTime
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID, ENUM
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import <PERSON>olean
from datetime import datetime, timezone
from decimal import Decimal

# uuid
import uuid


class Base(DeclarativeBase):
    pass


class Transactions(Base):
    __tablename__ = "transactions"

    id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=False)
    company_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=False)
    transaction_type: Column[str] = Column(type_=String(length=255), nullable=False)
    status: Column[str] = Column(type_=ENUM("COMPLETED", name="transactions_status_enum"), nullable=False)

    currency: Column[str] = Column(type_=String(length=10), nullable=False)
    target_platform_bank_account_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=False)
    amount_requested: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    amount_client_paid: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    amount_client_received: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    amount_company_received: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    total_fee_amount: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    total_commission_amount: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    available_balance: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=2), nullable=False)
    completed_at: Column[datetime] = Column(type_=DateTime(timezone=False), nullable=True)


class SnapshotTransactions(Base):
    __tablename__ = "snapshot_transactions"

    id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=True)
    company_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=False)
    transaction_type: Column[str] = Column(type_=String(length=255), nullable=False)
    currency: Column[str] = Column(type_=String(length=10), nullable=False)
    target_platform_bank_account_id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), nullable=False)

    counts_transaction: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=0), nullable=False)
    sum_amount_requested: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    sum_amount_client_paid: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    sum_amount_client_received: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    sum_amount_company_received: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    sum_total_fee_amount: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    sum_total_commission_amount: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)
    last_available_balance: Column[Decimal] = Column(type_=DECIMAL(precision=18, scale=6), nullable=False)

    start_completed_at: Column[datetime] = Column(type_=DateTime(timezone=False), nullable=True)
    last_completed_at: Column[datetime] = Column(type_=DateTime(timezone=False), nullable=True)

    created_at: Column[datetime] = Column(
        type_=DateTime(timezone=False), nullable=False, default=datetime.now(tz=timezone.utc).replace(tzinfo=None)
    )
    updated_at: Column[datetime] = Column(
        type_=DateTime(timezone=False),
        nullable=False,
        default=datetime.now(tz=timezone.utc).replace(tzinfo=None),
        onupdate=datetime.now(tz=timezone.utc).replace(tzinfo=None),
    )


class BankAccounts(Base):
    __tablename__ = "bank_accounts"

    id: Column[uuid.UUID] = Column(type_=PostgresUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    is_active: Column[bool] = Column(
        type_=Boolean,
        nullable=False,
        default=True,
    )
