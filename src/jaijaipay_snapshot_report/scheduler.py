#!/usr/bin/env python3.11
# coding:utf-8
# Copyright (C) 2024 All rights reserved.
# FILENAME:    ~~/src/wow2_bot_worker/scheduler.py
# VERSION:     0.4.2
# CREATED:     2024-08-19 14:26
# AUTHOR:      Cha<PERSON><PERSON> Srikajohnlap <<EMAIL>>
# DESCRIPTION:
#
# HISTORY:
# *************************************************************
"""Bot worker's pulse running on fixed schedule"""

### Standard packages ###
from asyncio import AbstractEventLoop, new_event_loop

### Third-party packages ###
from apscheduler.schedulers.asyncio import AsyncIOScheduler

### Local modules ###
from src.jaijaipay_snapshot_report.tasks.update_snapshot import create_snapshot_transaction


def pulse() -> None:
    # start
    print("Scheduler Start ...")
    loop: AbstractEventLoop = new_event_loop()
    # Create Independent tasks scheduler
    independent_scheduler: AsyncIOScheduler = AsyncIOScheduler(event_loop=loop)
    # Do every 20 sec
    independent_scheduler.add_job(
        func=create_snapshot_transaction,
        trigger="interval",
        seconds=20,
        max_instances=1,
        misfire_grace_time=120,
        coalesce=True,
    )

    independent_scheduler.start()
    try:
        loop.run_forever()
    except KeyboardInterrupt:
        pass
    finally:
        independent_scheduler.shutdown()


if __name__ == "__main__":
    pulse()
