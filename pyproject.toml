[build-system]
build-backend = 'hatchling.build'
requires = [ 'hatchling' ]


[project]
authors = [
  { email = '<EMAIL>', name = 'test01' },
]
dependencies = [
  "apscheduler>=3.11.0",
  "asyncpg>=0.30.0",
  "greenlet>=3.2.3",
  "polars>=1.31.0",
  'pydantic-settings >=2.3.4',
  "sqlalchemy>=2.0.41",
]
description = 'JaijaiPay'
license = 'Unlicense'
maintainers = [
  { email = '<EMAIL>', name = 'test01' },
]
name = 'jaijaipay-snapshot-report'
readme = 'README.md'
requires-python = '>=3.13.2'
version = '0.0.1'


[tool.hatch.build]
packages = ['src' ]


[tool.pytest.ini_options]
python_files = '*.py'
testpaths = [ 'tests' ]


[tool.ruff]
line-length = 120



[tool.ruff.lint.per-file-ignores]
'__init__.py' = ['F401'] # Ignore unused imports

